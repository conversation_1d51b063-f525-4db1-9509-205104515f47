/**
 * 提示词删除确认模态窗口组件
 */
import React, { useState } from 'react';
import { Modal } from '@/components/common/modals';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  prompt: UserPrompt | null;
}

export default function PromptDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  prompt
}: PromptDeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
    } finally {
      setIsDeleting(false);
    }
  };

  if (!prompt) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="删除提示词"
      maxWidth="max-w-md"
    >
      <div className="space-y-4">
        {/* 警告图标和消息 */}
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <span className="material-icons text-red-500 text-2xl">warning</span>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-text-dark mb-2">
              确认删除提示词
            </h3>
            <p className="text-text-medium mb-4">
              您确定要删除提示词 <span className="font-medium text-text-dark">"{prompt.title}"</span> 吗？
            </p>
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">
                <span className="material-icons text-sm mr-1 align-middle">info</span>
                此操作无法撤销，删除后将永久丢失该提示词的所有内容。
              </p>
            </div>
          </div>
        </div>

        {/* 按钮组 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-[rgba(120,180,140,0.2)]">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-text-medium hover:text-text-dark transition-colors"
            disabled={isDeleting}
          >
            取消
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isDeleting}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isDeleting && (
              <span className="material-icons animate-spin text-sm mr-2">refresh</span>
            )}
            {isDeleting ? '删除中...' : '确认删除'}
          </button>
        </div>
      </div>
    </Modal>
  );
}
