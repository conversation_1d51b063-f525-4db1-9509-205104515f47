/**
 * 提示词表单模态窗口组件
 * 用于创建和编辑用户提示词
 */
import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (prompt: UserPrompt) => void;
  prompt?: UserPrompt | null; // 编辑时传入现有提示词
  promptType: string; // 提示词类型
}

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作' },
  'ai_polishing': { label: 'AI润色' },
  'character': { label: '角色塑造' },
  'worldbuilding': { label: '世界观构建' }
} as const;

export default function PromptFormModal({
  isOpen,
  onClose,
  onSave,
  prompt,
  promptType
}: PromptFormModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: promptType,
    content: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 当模态窗口打开或提示词数据变化时，初始化表单
  useEffect(() => {
    if (isOpen) {
      if (prompt) {
        // 编辑模式：填充现有数据
        setFormData({
          title: prompt.title || '',
          description: prompt.description || '',
          type: prompt.type || promptType,
          content: prompt.content || ''
        });
      } else {
        // 创建模式：重置表单
        setFormData({
          title: '',
          description: '',
          type: promptType,
          content: ''
        });
      }
      setError(null);
    }
  }, [isOpen, prompt, promptType]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setError('请输入提示词标题');
      return;
    }
    
    if (!formData.content.trim()) {
      setError('请输入提示词内容');
      return;
    }

    if (formData.content.length > 10000) {
      setError('提示词内容不能超过10000字');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const requestData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        content: formData.content.trim()
      };

      // 如果是编辑模式，添加ID
      if (prompt?.id) {
        (requestData as any).id = prompt.id;
      }

      const response = await fetch('/api/prompt', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '操作失败');
      }

      if (data.success) {
        // 调用父组件的保存回调
        onSave(data.data);
        onClose();
      } else {
        throw new Error(data.error || '操作失败');
      }
    } catch (error) {
      console.error('保存提示词失败:', error);
      setError(error instanceof Error ? error.message : '保存失败，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // 清除错误信息
    if (error) {
      setError(null);
    }
  };

  const isEditMode = !!prompt?.id;
  const modalTitle = isEditMode ? '编辑提示词' : '创建提示词';
  const typeLabel = promptTypeMap[promptType as keyof typeof promptTypeMap]?.label || promptType;

  // 按钮组件
  const footerButtons = (
    <div className="flex justify-end space-x-3">
      <button
        type="button"
        onClick={onClose}
        className="px-4 py-2 text-text-medium hover:text-text-dark transition-colors"
        disabled={isLoading}
      >
        取消
      </button>
      <button
        type="submit"
        form="prompt-form"
        disabled={isLoading}
        className="px-6 py-2 bg-[#00C250] text-white rounded-lg hover:bg-[#00A844] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        {isLoading && (
          <span className="material-icons animate-spin text-sm mr-2">refresh</span>
        )}
        {isLoading ? '保存中...' : (isEditMode ? '更新' : '创建')}
      </button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${modalTitle} - ${typeLabel}`}
      maxWidth="max-w-4xl"
      footer={footerButtons}
    >
      <div className="h-full overflow-y-auto">
        <form id="prompt-form" onSubmit={handleSubmit} className="space-y-6">
          {/* 错误提示 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <span className="material-icons text-red-600 text-sm mr-2">error</span>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* 标题输入 */}
          <div>
            <label className="block text-sm font-medium text-text-dark mb-2">
              提示词标题 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full px-3 py-2 border border-[rgba(120,180,140,0.3)] rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              placeholder="请输入提示词标题"
              maxLength={100}
              required
            />
          </div>

          {/* 描述输入 */}
          <div>
            <label className="block text-sm font-medium text-text-dark mb-2">
              提示词描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full px-3 py-2 border border-[rgba(120,180,140,0.3)] rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent resize-none"
              placeholder="请输入提示词描述（可选）"
              rows={3}
              maxLength={500}
            />
          </div>

          {/* 内容输入 */}
          <div>
            <label className="block text-sm font-medium text-text-dark mb-2">
              提示词内容 <span className="text-red-500">*</span>
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              className="w-full px-3 py-2 border border-[rgba(120,180,140,0.3)] rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent resize-none"
              placeholder="请输入提示词内容"
              rows={8}
              maxLength={10000}
              required
            />
            <div className="mt-1 text-xs text-text-medium text-right">
              {formData.content.length}/10000 字
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
}
