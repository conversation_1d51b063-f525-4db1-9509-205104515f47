/**
 * 通用提示词选择模态窗口组件 - 用户提示词选择
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal } from '@/components/common/modals';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (prompt: UserPrompt) => void;
  promptType?: 'ai_writing' | 'ai_polishing';
  initialSelectedId?: string;
}

// 提示词类型标签映射
const PROMPT_TYPE_LABELS_MAP = {
  'ai_writing': 'AI写作',
  'ai_polishing': 'AI润色'
};

// 提示词类型颜色映射
const promptTypeColors = {
  'ai_writing': 'bg-[#5a9d6b] text-[#5a9d6b]',
  'ai_polishing': 'bg-[#7D85CC] text-[#7D85CC]'
};

/**
 * 通用提示词选择模态窗口组件
 */
export const PromptSelectionModal: React.FC<PromptSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  promptType,
  initialSelectedId
}) => {
  // 状态
  const [userPrompts, setUserPrompts] = useState<UserPrompt[]>([]);
  const [filteredPrompts, setFilteredPrompts] = useState<UserPrompt[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPrompt, setSelectedPrompt] = useState<UserPrompt | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>(promptType || 'ai_writing');

  // 当promptType变化时更新selectedCategory
  useEffect(() => {
    if (promptType) {
      setSelectedCategory(promptType);
    }
  }, [promptType]);

  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 加载用户提示词
  const loadUserPrompts = useCallback(async () => {
    try {
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: selectedCategory,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const prompts = data.data || [];
          setUserPrompts(prompts);
          return prompts;
        }
      }
      return [];
    } catch (error) {
      console.error('加载用户提示词失败:', error);
      throw error;
    }
  }, [selectedCategory]);

  // 加载提示词
  const loadPrompts = useCallback(async () => {
    if (!isOpen) return;

    setIsLoading(true);
    setError('');

    try {
      const userPromptsData = await loadUserPrompts();

      // 如果有初始选中的提示词ID，设置选中状态
      if (initialSelectedId) {
        const selected = userPromptsData.find(p => String(p.id) === String(initialSelectedId));
        if (selected) {
          setSelectedPrompt(selected);
        }
      }
    } catch (error) {
      console.error('加载提示词失败:', error);
      setError('加载提示词失败');
    } finally {
      setIsLoading(false);
    }
  }, [isOpen, loadUserPrompts, initialSelectedId]);

  // 过滤提示词
  useEffect(() => {
    let filtered = userPrompts;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(prompt =>
        prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (prompt.description && prompt.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredPrompts(filtered);
  }, [userPrompts, searchTerm]);

  // 加载提示词
  useEffect(() => {
    loadPrompts();
  }, [loadPrompts]);

  // 处理提示词选择
  const handlePromptSelect = (prompt: UserPrompt) => {
    setSelectedPrompt(prompt);
    onSelect(prompt);
    onClose();
  };

  // 获取提示词类型标签
  const getPromptTypeLabel = (type: string): string => {
    return PROMPT_TYPE_LABELS_MAP[type as keyof typeof PROMPT_TYPE_LABELS_MAP] || type;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="选择提示词"
      maxWidth="max-w-4xl"
    >
      <div className="flex flex-col h-[70vh]">
        {/* 分类选择和搜索栏 */}
        <div className="mb-4 space-y-3">
          {/* 分类选择 */}
          <div className="flex space-x-2">
            {Object.entries(PROMPT_TYPE_LABELS_MAP).map(([type, label]) => (
              <button
                key={type}
                onClick={() => setSelectedCategory(type)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedCategory === type
                    ? 'bg-[#5a9d6b] text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {label}
              </button>
            ))}
          </div>

          {/* 搜索栏 */}
          <div className="relative">
            <span className="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-text-medium text-lg">
              search
            </span>
            <input
              type="text"
              placeholder="搜索提示词..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-[rgba(120,180,140,0.3)] rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
            />
          </div>
        </div>

        {/* 提示词列表 */}
        <div className="flex-1 overflow-hidden">
          <div
            ref={scrollContainerRef}
            className="h-full overflow-y-auto pr-2"
            style={{ scrollbarWidth: 'thin' }}
          >
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-green mx-auto mb-2"></div>
                  <p className="text-text-medium">加载中...</p>
                </div>
              </div>
            ) : error ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <span className="material-icons text-4xl text-red-400 mb-2">error</span>
                  <p className="text-red-600">{error}</p>
                </div>
              </div>
            ) : filteredPrompts.length === 0 ? (
              <div className="h-full flex items-center justify-center flex-col">
                <div className="w-20 h-20 bg-[#f7f2ea] rounded-full flex items-center justify-center mb-4">
                  <span className="material-icons text-4xl text-[#8a7c70]">lightbulb</span>
                </div>
                <h3 className="text-lg font-medium text-text-dark mb-2 font-ma-shan">
                  {searchTerm ? "没有找到匹配的提示词" : "暂无提示词"}
                </h3>
                <p className="text-text-medium text-center max-w-xs mb-6">
                  {searchTerm ? "尝试使用其他关键词搜索" : "暂无可用的官方提示词"}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredPrompts.map(prompt => (
                  <div
                    key={prompt.id}
                    className={`group p-3 rounded-lg transition-all duration-200 cursor-pointer relative ${
                      selectedPrompt?.id === prompt.id
                        ? 'bg-[#e6dfd0] border-2 border-[#6d5c4d]'
                        : 'bg-white border border-[rgba(120,180,140,0.15)] hover:bg-[#f0e9df] hover:border-[#6d5c4d]'
                    }`}
                    onClick={() => handlePromptSelect(prompt)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center mb-1">
                          <h3 className="font-medium text-text-dark truncate mr-2">
                            {prompt.title}
                          </h3>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-opacity-10 ${
                            promptTypeColors[prompt.type as keyof typeof promptTypeColors] || 'bg-gray-100 text-gray-800'
                          }`}>
                            官方
                          </span>
                        </div>
                        {prompt.description && (
                          <p className="text-sm text-text-medium line-clamp-2 mb-2">
                            {prompt.description}
                          </p>
                        )}
                        <div className="flex items-center text-xs text-text-light">
                          <span className="material-icons text-xs mr-1">schedule</span>
                          <span>{new Date(prompt.updated_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end pt-4 border-t border-[rgba(120,180,140,0.2)]">
          <button
            onClick={onClose}
            className="px-4 py-2 text-text-medium hover:text-text-dark transition-colors duration-200"
          >
            取消
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default PromptSelectionModal;
