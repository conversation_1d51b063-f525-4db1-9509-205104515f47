/**
 * AI流式API中间层路由
 * 接收前端请求，转发到目标AI服务器，返回流式响应
 */
import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';
import { createClient } from '@supabase/supabase-js';
import { BackendBillingService } from '@/lib/billing/backend';

// 多API配置支持不同模型使用不同密钥
const API_CONFIGS = {
  'ceshi': { // 测试版模型
    baseURL: "http://localhost:8080/v1",
    apiKey: "sk-BXFokKfUGI8D7USNzlXVR4kys0GARJ7Q8F6vqA5fX1hwcl6u"
  },
  'kelaode': { // 克劳德模型
    baseURL: "https://api.ai-wave.org/v1",
    apiKey: "sk-4htOG2lIoAizKHP_CjHJoqfRBuBt-0_s78EBPj-QJpvk5sCB6Ly_7BFkOz0"
  }
};

// 硬编码内部API密钥 - 用于调用计费API
const INTERNAL_API_KEY = "billing_internal_2024_secure_key_xyz789";

// 创建Supabase客户端用于获取用户信息
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);



// 消息类型
interface Message {
  role: 'user' | 'system' | 'assistant';
  content: string;
}

// 请求体类型
interface StreamRequest {
  messages: Message[];
  model: string;
  temperature?: number;
  max_tokens?: number;
}

// 模型代号到真实模型名称的转换映射表
const MODEL_CODE_TO_NAME: Record<string, string> = {
  'ceshi': 'gemini-2.5-pro-free', // 测试版
  'kelaode': 'anthropic/claude-sonnet-4', // 克劳德
};

// 转换模型代号为真实模型名称
const convertModelCodeToName = (modelCode: string): string => {
  const realModelName = MODEL_CODE_TO_NAME[modelCode];
  if (!realModelName) {
    console.warn(`未知的模型代号: ${modelCode}, 使用默认模型`);
    return 'gemini-2.5-pro'; // 默认使用测试版模型
  }
  console.log(`模型代号转换: ${modelCode} -> ${realModelName}`);
  return realModelName;
};

/**
 * 获取用户提示词内容
 */
const getUserPromptContent = async (promptId: string): Promise<string | null> => {
  try {
    console.log(`[Stream] 请求用户提示词ID: ${promptId}`);

    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/prompt?id=${promptId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`[Stream] 获取提示词失败: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    if (data.success && data.data?.content) {
      console.log(`[Stream] 成功获取用户提示词: ${data.data.title}`);
      return data.data.content;
    }

    console.error('[Stream] 提示词响应格式错误:', data);
    return null;
  } catch (error) {
    console.error('[Stream] 获取用户提示词异常:', error);
    return null;
  }
};

/**
 * 处理消息中的用户提示词ID
 */
const processUserPrompts = async (messages: Message[]): Promise<Message[]> => {
  const processedMessages: Message[] = [];

  for (const message of messages) {
    // 检查是否是系统消息
    if (message.role === 'system') {
      // 检查是否为用户提示词ID格式
      if (message.content.match(/^__USER_PROMPT_ID__:[a-zA-Z0-9-]+$/)) {
      try {
        // 提取用户提示词ID
        const promptIdMatch = message.content.match(/__USER_PROMPT_ID__:([a-zA-Z0-9-]+)/);
        if (!promptIdMatch) {
          console.error('[Stream] 无法提取用户提示词ID');
          processedMessages.push(message);
          continue;
        }

        const promptId = promptIdMatch[1];
        console.log(`[Stream] 检测到用户提示词ID: ${promptId}`);

        // 获取用户提示词内容（等待最多5秒）
        const timeoutPromise = new Promise<string | null>((resolve) => {
          setTimeout(() => resolve(null), 5000);
        });

        const contentPromise = getUserPromptContent(promptId);
        const promptContent = await Promise.race([contentPromise, timeoutPromise]);

        if (promptContent) {
          // 直接使用用户提示词内容，不添加任何前置包装
          processedMessages.push({
            role: 'system',
            content: promptContent
          });

          console.log('[Stream] 用户提示词处理成功，使用纯净内容');
        } else {
          console.error('[Stream] 获取用户提示词超时或失败，使用原始消息');
          processedMessages.push(message);
        }
      } catch (error) {
        console.error('[Stream] 处理用户提示词失败:', error);
        processedMessages.push(message);
      }
      } else {
        // 自定义提示词：直接使用原始内容，不添加任何前置包装
        console.log('[Stream] 检测到自定义提示词，使用纯净内容');

        processedMessages.push({
          role: 'system',
          content: message.content
        });

        console.log('[Stream] 自定义提示词处理成功');
      }
    } else {
      // 非系统消息，直接添加
      processedMessages.push(message);
    }
  }

  return processedMessages;
};

/**
 * 错误处理函数
 */
const handleAIError = (error: any): string => {
  console.error('AI服务错误:', error);
  const errorMessage = error?.message || JSON.stringify(error) || '未知错误';

  if (errorMessage.includes('API key not configured')) {
    return '401';
  }

  if (error instanceof OpenAI.APIError) {
    if (error.status === 401) {
      return '401';
    }
    if (error.status === 429) {
      return '429';
    }
    if (error.status === 403) {
      return '403';
    }
    if (error.status === 500) {
      return '500';
    }
    if (error.status === 503) {
      return '503';
    }
    if (error.code === 'invalid_api_key') {
      return '401';
    }
    // 返回具体的状态码，如果没有状态码则返回500
    return error.status ? error.status.toString() : '500';
  }

  if (errorMessage.includes('token') || errorMessage.includes('context_length_exceeded')) {
    return '413';
  }
  if (errorMessage.includes('network') || errorMessage.includes('timeout') || errorMessage.includes('fetch failed')) {
    return '503';
  }
  if (errorMessage.includes('authentication') || errorMessage.includes('认证')) {
    return '401';
  }

  return '500';
};

/**
 * 获取用户UUID和访问令牌
 */
const getUserInfo = async (request: NextRequest): Promise<{ userUuid: string; accessToken: string } | null> => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) return null;

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) return null;
    return {
      userUuid: user.id,
      accessToken: token
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

/**
 * 调用内部计费API进行扣费
 */
const callBillingAPI = async (
  userUuid: string,
  amount: number,
  modelCode?: string
): Promise<{ success: boolean; message?: string }> => {
  try {
    // 构建完整的URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const billingUrl = `${baseUrl}/api/billing/deduct`;

    console.log(`[Stream] 调用计费API: ${billingUrl}`);
    console.log(`[Stream] 计费参数: 用户=${userUuid}, 金额=${amount}, 模型=${modelCode || '未指定'}`);

    const response = await fetch(billingUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        internalApiKey: INTERNAL_API_KEY,
        userUuid,
        amount,
        modelCode
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[Stream] 计费API调用失败: ${response.status} ${response.statusText}`);
      console.error(`[Stream] 错误详情: ${errorText}`);
      return {
        success: false,
        message: `计费API调用失败: ${response.status} - ${errorText}`
      };
    }

    const result = await response.json();
    console.log(`[Stream] 计费API响应:`, result);
    return result;
  } catch (error) {
    console.error('[Stream] 调用计费API异常:', error);
    return {
      success: false,
      message: '调用计费API异常'
    };
  }
};

// 模型计费倍率配置表
const MODEL_BILLING_RATES = {
  'ceshi': { // 测试模型
    inputRate: 5,   // 输入倍率
    outputRate: 40  // 输出倍率
  },
  'kelaode': { // 克劳德模型
    inputRate: 10,  // 输入倍率
    outputRate: 55  // 输出倍率
  }
};

/**
 * 计算Token消耗的字数（新倍率计算方式）
 */
const calculateTokenCost = (promptTokens: number, totalTokens: number, modelCode: string): number => {
  // 输出token = 总token - 输入token
  const outputTokens = totalTokens - promptTokens;

  // 获取模型倍率配置，如果模型不存在则使用测试模型的倍率
  const rates = MODEL_BILLING_RATES[modelCode as keyof typeof MODEL_BILLING_RATES] || MODEL_BILLING_RATES['ceshi'];

  // 修正后的倍率计算：输入token * 输入倍率 + 输出token * 输出倍率
  const inputCost = promptTokens * rates.inputRate;
  const outputCost = outputTokens * rates.outputRate;
  const totalCost = inputCost + outputCost;

  console.log(`[计费] 模型${modelCode}: 输入${promptTokens}token×${rates.inputRate}倍=${inputCost}字, 输出${outputTokens}token×${rates.outputRate}倍=${outputCost}字, 总消耗${totalCost}字`);

  return totalCost;
};

/**
 * 获取指定模型的API配置
 */
const getAPIConfig = (modelCode: string) => {
  return API_CONFIGS[modelCode as keyof typeof API_CONFIGS] || API_CONFIGS['ceshi'];
};

/**
 * 创建OpenAI客户端实例
 */
const createOpenAIClient = (modelCode: string): OpenAI => {
  const config = getAPIConfig(modelCode);
  return new OpenAI({
    apiKey: config.apiKey,
    baseURL: config.baseURL,
  });
};

/**
 * POST 方法处理流式AI请求
 */
export async function POST(request: NextRequest) {
  let userUuid: string | null = null;
  let accessToken: string | null = null;

  try {
    // 解析请求体
    const body: StreamRequest = await request.json();
    const { messages, model, temperature = 0.7, max_tokens = 64000 } = body;

    // 验证请求参数
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: '400' },
        { status: 400 }
      );
    }

    if (!model) {
      return NextResponse.json(
        { error: '400' },
        { status: 400 }
      );
    }

    // 获取用户信息（UUID和访问令牌）
    const userInfo = await getUserInfo(request);
    if (userInfo) {
      userUuid = userInfo.userUuid;
      accessToken = userInfo.accessToken;
      console.log('用户UUID:', userUuid ? '已获取' : '未获取');
      console.log('访问令牌:', accessToken ? '已获取' : '未获取');
    }

    // 检查用户权限 - 在处理AI请求前进行权限验证（包括模型权限）
    if (userUuid && accessToken) {
      const canUseResult = await BackendBillingService.canUseAI(userUuid, 100, accessToken, model); // 传递模型代号
      if (!canUseResult.canUse) {
        console.log(`[Stream] 用户权限检查失败: ${canUseResult.message}`);
        return NextResponse.json(
          { error: '403' },
          { status: 403 }
        );
      }
      console.log(`[Stream] 用户权限检查通过，模型: ${model}`);
    } else {
      console.log(`[Stream] 未获取到用户信息，拒绝AI请求`);
      return NextResponse.json(
        { error: '401' },
        { status: 401 }
      );
    }

    // 转换模型代号为真实模型名称
    const realModelName = convertModelCodeToName(model);

    console.log(`后端流式生成使用模型: ${model} -> ${realModelName}`);
    console.log("后端发送流式请求:", {
      model: realModelName,
      messages: messages.map(m => ({
        role: m.role,
        content: m.content
      })),
      temperature
    });

    // 处理用户提示词
    const processedMessages = await processUserPrompts(messages);

    // 预扣费机制：统计所有消息内容的字符数
    const totalCharacters = processedMessages.reduce((total, message) => {
      return total + (message.content?.length || 0);
    }, 0);

    console.log(`[Stream] 预扣费检查: 预估消耗${totalCharacters}字符`);

    // 预扣费检查：验证用户余额是否足够支付预估消耗（包括模型权限）
    if (userUuid && accessToken) {
      const preCheckResult = await BackendBillingService.canUseAI(userUuid, totalCharacters, accessToken, model);
      if (!preCheckResult.canUse) {
        console.log(`[Stream] 预扣费检查失败: 预估${totalCharacters}字符, ${preCheckResult.message}`);
        return NextResponse.json(
          { error: '403' },
          { status: 403 }
        );
      }
      console.log(`[Stream] 预扣费检查通过: 预估${totalCharacters}字符, 当前余额${preCheckResult.balance}字符, 模型: ${model}`);
    }

    // 创建OpenAI客户端
    const client = createOpenAIClient(model);

    // 调用OpenAI流式API（使用处理后的消息和转换后的真实模型名称）
    const stream = await client.chat.completions.create({
      model: realModelName,
      messages: processedMessages.map(m => ({ role: m.role, content: m.content })),
      temperature,
      max_tokens,
      stream: true,
      stream_options: { include_usage: true }
    });

    console.log("后端Stream created successfully");

    // 创建可读流来转发响应
    const readableStream = new ReadableStream({
      async start(controller) {
        let pendingChars: string[] = []; // 后端字符队列
        let isProcessing = false; // 是否正在处理发送
        let finalUsage: any = null; // 存储最终的usage信息
        let hasBilled = false; // 防止重复计费的标志
        let streamEnded = false; // 标记AI流是否结束

        // 发送最终usage信息和计费的函数
        const sendFinalUsage = async () => {
          if (finalUsage && !hasBilled) {
            // 显示最终usage信息日志
            console.log("流结束，最终usage信息:", finalUsage);

            // 发送最终usage信息给前端
            const encoder = new TextEncoder();
            const usageData = `\n__USAGE_DATA__:${JSON.stringify(finalUsage)}`;
            controller.enqueue(encoder.encode(usageData));

            // 进行计费
            if (userUuid) {
              hasBilled = true; // 设置计费标志
              try {
                const promptTokens = finalUsage.prompt_tokens || 0;
                const totalTokens = finalUsage.total_tokens || 0;
                const outputTokens = totalTokens - promptTokens;

                // 使用新的倍率计算方式
                const totalCost = calculateTokenCost(promptTokens, totalTokens, model);

                console.log(`最终计费信息: 输入${promptTokens}token, 输出${outputTokens}token(总${totalTokens}-输入${promptTokens}), 总消耗${totalCost}字`);

                // 调用内部计费API进行扣费，传递模型代号
                const billingResult = await callBillingAPI(userUuid, totalCost, model);
                if (billingResult.success) {
                  console.log(`最终计费成功`);
                } else {
                  console.error(`最终计费失败: ${billingResult.message}`);
                }
              } catch (billingError) {
                console.error('最终计费处理异常:', billingError);
              }
            } else {
              console.log('未获取到用户UUID，跳过计费');
            }
          }
          controller.close();
        };

        // 定时发送字符的函数（模拟前端requestAnimationFrame的节奏）
        const processCharQueue = () => {
          if (pendingChars.length > 0 && !isProcessing) {
            isProcessing = true;

            // 取出一个字符并发送
            const char = pendingChars.shift() as string;
            const encoder = new TextEncoder();
            controller.enqueue(encoder.encode(char));

            isProcessing = false;

            // 继续处理下一个字符（16ms间隔模拟60fps）
            if (pendingChars.length > 0) {
              setTimeout(processCharQueue, 16);
            } else if (streamEnded) {
              // 如果队列空了且流已结束，发送usage数据
              sendFinalUsage();
            }
          }
        };

        try {
          // 处理AI流的chunk
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';

            // 处理普通内容
            if (content) {
              // 将content拆分为字符并加入队列
              for (const char of content) {
                pendingChars.push(char);
              }

              // 如果没有在处理，开始处理队列
              if (!isProcessing && pendingChars.length > 0) {
                processCharQueue();
              }
            }

            // 收集usage信息，但不发送给前端，也不显示日志
            if (chunk.usage) {
              finalUsage = chunk.usage; // 保存最新的usage信息
              // 不在这里显示usage日志，等流结束后统一显示和发送
            }
          }

          streamEnded = true;
          // 如果队列为空，立即发送usage；否则等队列处理完
          if (pendingChars.length === 0) {
            await sendFinalUsage();
          }

        } catch (error) {
          console.error("后端流式处理错误:", error);
          const errorMessage = handleAIError(error);
          const encoder = new TextEncoder();
          controller.enqueue(encoder.encode(`\n\nERROR: ${errorMessage}`));
          controller.close();
        }
      }
    });

    // 返回流式响应
    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no',
        'Transfer-Encoding': 'chunked',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error: any) {
    console.error("后端API请求错误:", error);

    // 添加更详细的错误信息
    if (error.status) console.error(`错误状态码: ${error.status}`);
    if (error.message) console.error(`错误消息: ${error.message}`);
    if (error.code) console.error(`错误代码: ${error.code}`);
    if (error.type) console.error(`错误类型: ${error.type}`);

    const errorMessage = handleAIError(error);
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
