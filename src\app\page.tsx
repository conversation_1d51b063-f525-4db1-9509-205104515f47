'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

import TopBar from '@/components/TopBar';
import { useAuth } from '@/hooks/useAuth';
import { useAIChat } from '@/contexts/AIChatContext';
import UserAccountButton from '@/components/auth/UserAccountButton';
import RechargeModal from '@/components/common/modals/RechargeModal';

export default function Home() {
  // 修改默认选中的菜单为小说创作
  const [activeMenu, setActiveMenu] = useState('novel');
  const router = useRouter();
  const { user, isAuthenticated, signOut } = useAuth();
  const { openChat } = useAIChat();

  // 添加弹窗状态
  const [showRechargeModal, setShowRechargeModal] = useState(false);

  // 创建卡片内容，避免条件渲染导致的问题
  const renderCards = () => {
    return (
      <>
        <div className="ghibli-card group cursor-pointer h-80 text-center" onClick={() => router.push('/works')}>
          <div className="tape" style={{ backgroundColor: 'rgba(125,133,204,0.7)' }}>
            <div className="tape-texture"></div>
          </div>
          <div className="flex flex-col items-center">
            <span className="material-icons text-[#7D85CC] mb-6" style={{ fontSize: '56px' }}>auto_stories</span>
            <h3 className="font-medium text-text-dark text-xl mb-4" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>我的作品</h3>
            <p className="text-text-medium text-sm mb-6">探索和管理你的创作，随时继续你的写作</p>
            <button className="px-5 py-2 rounded-full bg-[#7D85CC] text-white hover:bg-[#6970B9] transition-colors duration-200">查看作品集</button>
          </div>
          <div className="page-curl"></div>
        </div>



        <div className="ghibli-card h-80 text-center">
          <div className="tape" style={{ backgroundColor: 'rgba(224,149,117,0.7)' }}>
            <div className="tape-texture"></div>
          </div>
          <div className="flex flex-col items-center">
            <svg className="w-14 h-14 mb-6 fill-current text-[#E0976F]" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M7,10L12,15L17,10H7Z" />
            </svg>
            <h3 className="font-medium text-text-dark text-xl mb-4" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>模板中心</h3>
            <p className="text-text-medium text-sm mb-6">使用专业模板快速开始你的创作历程</p>
            <div className="grid grid-cols-2 gap-3 px-2">
              <div className="bg-white bg-opacity-50 p-2 rounded-lg flex items-center space-x-1">
                <span className="material-icons text-[#9C6FE0] text-sm">psychology</span>
                <span className="text-xs text-text-medium font-medium">奇幻</span>
              </div>
              <div className="bg-white bg-opacity-50 p-2 rounded-lg flex items-center space-x-1">
                <span className="material-icons text-[#E06F9C] text-sm">favorite</span>
                <span className="text-xs text-text-medium font-medium">爱情</span>
              </div>
              <div className="bg-white bg-opacity-50 p-2 rounded-lg flex items-center space-x-1">
                <span className="material-icons text-[#6F9CE0] text-sm">rocket_launch</span>
                <span className="text-xs text-text-medium font-medium">科幻</span>
              </div>
              <div className="bg-white bg-opacity-50 p-2 rounded-lg flex items-center space-x-1">
                <span className="material-icons text-[#E0C56F] text-sm">local_police</span>
                <span className="text-xs text-text-medium font-medium">悬疑</span>
              </div>
            </div>
          </div>
          <div className="page-curl"></div>
        </div>

        <div className="ghibli-card group cursor-pointer h-80 text-center" onClick={openChat}>
          <div className="tape" style={{ backgroundColor: 'rgba(90,157,107,0.7)' }}>
            <div className="tape-texture"></div>
          </div>
          <div className="flex flex-col items-center">
            <span className="material-icons text-[#5a9d6b] mb-6" style={{ fontSize: '56px' }}>chat</span>
            <h3 className="font-medium text-text-dark text-xl mb-4" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>AI智能对话</h3>
            <p className="text-text-medium text-sm mb-6">与AI助手对话，获得创作灵感和写作建议</p>
            <button className="px-5 py-2 rounded-full bg-[#5a9d6b] text-white hover:bg-[#4a8d5b] transition-colors duration-200">开始对话</button>
          </div>
          <div className="page-curl"></div>
        </div>
      </>
    );
  };

  return (
      <div className="flex h-screen animate-fadeIn overflow-hidden">
        {/* 背景网格 */}
        <div className="grid-background"></div>

        {/* 装饰元素，在小屏幕上减少数量 */}
        <div className="dot hidden md:block" style={{ top: "120px", left: "15%" }}></div>
        <div className="dot" style={{ bottom: "80px", right: "20%" }}></div>
        <div className="dot hidden md:block" style={{ top: "30%", right: "25%" }}></div>
        <div className="dot hidden md:block" style={{ bottom: "40%", left: "30%" }}></div>

        <svg className="wave hidden md:block" style={{ bottom: "20px", left: "10%" }} width="100" height="20" viewBox="0 0 100 20">
          <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
        </svg>

        <svg className="wave hidden md:block" style={{ top: "15%", right: "5%" }} width="100" height="20" viewBox="0 0 100 20">
          <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
        </svg>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col h-full overflow-hidden transition-all duration-300">
        {/* 使用通用顶边栏组件 */}
        <TopBar
          actions={
            <div className="flex items-center space-x-2">
              {isAuthenticated ? (
                <UserAccountButton />
              ) : (
                <>
                  <button
                    className="ghibli-button outline text-sm"
                    onClick={() => setShowRechargeModal(true)}
                  >
                    <span className="material-icons mr-1 text-sm">account_balance_wallet</span>
                    充值
                  </button>
                  <button
                    className="ghibli-button outline text-sm"
                    onClick={() => router.push('/register')}
                  >
                    <span className="material-icons mr-1 text-sm">person_add</span>
                    注册
                  </button>
                  <button
                    className="ghibli-button outline text-sm"
                    onClick={() => router.push('/login')}
                  >
                    <span className="material-icons mr-1 text-sm">login</span>
                    登录
                  </button>
                </>
              )}
            </div>
          }
        />

        {/* 主要内容 - 添加flex-1和overflow-auto确保内容区域可滚动但不影响外部容器 */}
        <div className="flex-1 p-4 md:p-6 lg:p-8 overflow-auto" style={{ scrollbarWidth: 'thin' }}>
          <div className="max-w-6xl mx-auto">
            <h2 className="section-title flex items-center text-base md:text-lg lg:text-xl">
              <span className="material-icons text-primary-green mr-2">dashboard</span>
              开启你的创作旅程
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mt-4 md:mt-6">
              {renderCards()}
            </div>


          </div>
        </div>
      </div>



      {/* 充值模态窗口 */}
      <RechargeModal
        isOpen={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
      />
    </div>
  );
}
