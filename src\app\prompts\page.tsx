'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import TopBar from '@/components/TopBar';
import { PromptGroup } from '@/types/ui';
import PromptFormModal from '@/components/prompts/PromptFormModal';
import PromptDeleteModal from '@/components/prompts/PromptDeleteModal';
import { getCurrentUser } from '@/services/userService';

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作', color: 'bg-[#5a9d6b] text-white', icon: 'create', group: 'novel', gradient: 'from-[#5a9d6b] to-[#4a8d5b]' },
  'ai_polishing': { label: 'AI润色', color: 'bg-[#7D85CC] text-white', icon: 'auto_fix_high', group: 'novel', gradient: 'from-[#7D85CC] to-[#6F9CE0]' }
};

// 提示词分组定义
const promptGroups: PromptGroup[] = [
  {
    label: '小说创作',
    color: 'bg-[#5a9d6b] text-white',
    icon: 'auto_stories',
    types: ['ai_writing', 'ai_polishing']
  }
];

export default function PromptsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [activeGroup, setActiveGroup] = useState(0);
  const [activeType, setActiveType] = useState('');
  const [userPrompts, setUserPrompts] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentUser, setCurrentUser] = useState<any>(null);

  // 模态窗口状态
  const [showFormModal, setShowFormModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<any>(null);
  const [deletingPrompt, setDeletingPrompt] = useState<any>(null);

  // 初始化默认选中的类型和用户信息
  useEffect(() => {
    if (promptGroups.length > 0 && promptGroups[activeGroup].types.length > 0) {
      const defaultType = promptGroups[activeGroup].types[0];
      setActiveType(defaultType);
    }

    // 获取当前用户信息
    const loadCurrentUser = async () => {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };

    loadCurrentUser();
  }, [activeGroup]);

  // 加载用户提示词数据
  const loadUserPrompts = async (type: string) => {
    try {
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: type,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserPrompts(data.data || []);
        }
      }
    } catch (error) {
      console.error('加载用户提示词失败:', error);
      setUserPrompts([]);
    }
  };

  // 加载提示词数据
  const loadPrompts = async (type: string) => {
    try {
      setIsLoading(true);
      await loadUserPrompts(type);
    } catch (error) {
      console.error('加载提示词失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 当选中类型改变时加载提示词
  useEffect(() => {
    if (activeType) {
      loadPrompts(activeType);
    }
  }, [activeType]);

  // 管理功能处理函数
  const handleCreatePrompt = () => {
    setEditingPrompt(null);
    setShowFormModal(true);
  };

  const handleEditPrompt = (prompt: any) => {
    setEditingPrompt(prompt);
    setShowFormModal(true);
  };

  const handleDeletePrompt = (prompt: any) => {
    setDeletingPrompt(prompt);
    setShowDeleteModal(true);
  };

  const handleSavePrompt = async (savedPrompt: any) => {
    // 重新加载提示词列表
    if (activeType) {
      await loadPrompts(activeType);
    }
  };

  const handleConfirmDelete = async () => {
    if (!deletingPrompt) return;

    try {
      const response = await fetch(`/api/prompt?id=${deletingPrompt.id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '删除失败');
      }

      if (data.success) {
        // 重新加载提示词列表
        if (activeType) {
          await loadPrompts(activeType);
        }
        setShowDeleteModal(false);
        setDeletingPrompt(null);
      } else {
        throw new Error(data.error || '删除失败');
      }
    } catch (error) {
      console.error('删除提示词失败:', error);
      alert(error instanceof Error ? error.message : '删除失败，请稍后再试');
    }
  };

  // 检查是否为当前用户创建的提示词
  const isOwnPrompt = (prompt: any) => {
    return currentUser && prompt.created_by === currentUser.email;
  };

  // 处理大分类切换
  const handleGroupChange = (groupIndex: number) => {
    setActiveGroup(groupIndex);
    const newType = promptGroups[groupIndex].types[0];
    setActiveType(newType);
  };

  // 处理具体类型切换
  const handleTypeChange = (type: string) => {
    setActiveType(type);
  };

  // 过滤提示词
  const filteredPrompts = userPrompts.filter(prompt => {
    const matchesSearch =
      prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (prompt.content && prompt.content.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (prompt.description && prompt.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });



  return (
    <div className="flex h-screen bg-bg-color animate-fadeIn overflow-hidden">
      {/* 背景网格 */}
      <div className="grid-background"></div>

      {/* 装饰元素，在小屏幕上减少数量 */}
      <div className="dot hidden md:block" style={{ top: "120px", left: "15%" }}></div>
      <div className="dot" style={{ bottom: "80px", right: "20%" }}></div>
      <div className="dot hidden md:block" style={{ top: "30%", right: "25%" }}></div>
      <div className="dot hidden md:block" style={{ bottom: "40%", left: "30%" }}></div>

      <svg className="wave hidden md:block" style={{ bottom: "20px", left: "10%" }} width="100" height="20" viewBox="0 0 100 20">
        <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
      </svg>

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          showBackButton={true}
        />
        {/* 横向分类标签导航 */}
        <div className="border-b border-[rgba(120,180,140,0.2)] bg-card-color">
          <div className="w-full px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              {/* 左边：大分类选择器 */}
              <div className="flex space-x-0">
                {promptGroups.map((group, index) => (
                  <button
                    key={group.label}
                    className={`relative px-6 py-4 text-sm font-medium transition-all duration-200 border-b-2 ${
                      activeGroup === index
                        ? 'border-[#00C250] text-[#00C250] bg-[rgba(0,194,80,0.05)]'
                        : 'border-transparent text-text-medium hover:text-text-dark hover:border-[rgba(120,180,140,0.3)]'
                    }`}
                    onClick={() => handleGroupChange(index)}
                  >
                    <div className="flex items-center">
                      <span className={`material-icons text-lg mr-2 ${
                        activeGroup === index ? 'text-[#00C250]' : 'text-text-medium'
                      }`}>
                        {group.icon}
                      </span>
                      {group.label}
                    </div>
                  </button>
                ))}
              </div>

              {/* 竖线分隔符 */}
              <div className="h-8 w-px bg-[rgba(120,180,140,0.3)] mx-4"></div>

              {/* 右边：当前大分类下的具体类型选项 */}
              <div className="flex space-x-0 flex-1">
                {promptGroups[activeGroup].types.map(type => (
                  <button
                    key={type}
                    className={`relative px-4 py-4 text-sm font-medium transition-all duration-200 border-b-2 ${
                      activeType === type
                        ? 'border-[#00C250] text-[#00C250] bg-[rgba(0,194,80,0.05)]'
                        : 'border-transparent text-text-medium hover:text-text-dark hover:border-[rgba(120,180,140,0.3)]'
                    }`}
                    onClick={() => handleTypeChange(type)}
                  >
                    <div className="flex items-center">
                      <span className={`material-icons text-base mr-1 ${
                        activeType === type ? 'text-[#00C250]' : 'text-text-medium'
                      }`}>
                        {promptTypeMap[type as keyof typeof promptTypeMap]?.icon}
                      </span>
                      {promptTypeMap[type as keyof typeof promptTypeMap]?.label}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
          <div className="w-full">
            {/* 工具栏：搜索框和创建按钮 */}
            <div className="relative mb-6 flex flex-wrap items-center gap-4 justify-between">
              {/* 搜索框 */}
              <div className="flex-shrink-0 w-64">
                <div className="relative w-full">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="material-icons text-text-light">search</span>
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-[rgba(120,180,140,0.3)] rounded-xl bg-card-color focus:outline-none focus:ring-2 focus:ring-[rgba(120,180,140,0.5)] shadow-sm text-text-dark"
                    placeholder="搜索提示词..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 创建按钮 */}
              {currentUser && (
                <button
                  onClick={handleCreatePrompt}
                  className="flex items-center px-4 py-2 bg-[#00C250] text-white rounded-lg hover:bg-[#00A844] transition-colors"
                >
                  <span className="material-icons text-sm mr-2">add</span>
                  创建提示词
                </button>
              )}
            </div>

            {/* 提示词卡片网格 */}
            <div className="flex flex-wrap gap-6">
              {filteredPrompts.length > 0 ? (
                filteredPrompts.map((prompt) => {
                  const typeConfig = promptTypeMap[activeType as keyof typeof promptTypeMap];
                  if (!typeConfig) return null;

                  const colorText = typeConfig.color.split(' ')[1];
                  const bgColor = typeConfig.color.split(' ')[0];

                  return (
                    <div
                      key={prompt.id}
                      className="ghibli-card cursor-pointer animate-fadeIn group relative"
                      style={{ width: '320px', height: '320px' }}
                      onClick={() => {/* TODO: 打开详情模态窗口 */}}
                    >
                      <div className="flex flex-col h-full">
                        {/* 顶部LOGO和标题在同一行 */}
                        <div className="flex items-center mb-4">
                          <div className={`w-12 h-12 rounded-full ${bgColor} flex items-center justify-center mr-3`}>
                            <span className={`material-icons text-xl ${colorText}`}>{typeConfig.icon}</span>
                          </div>
                          <h3 className="font-medium text-text-dark text-xl font-ma-shan flex-1">
                            {prompt.title}
                          </h3>

                          {/* 管理按钮 - 只对自己创建的提示词显示 */}
                          {isOwnPrompt(prompt) && (
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditPrompt(prompt);
                                }}
                                className="p-1 text-text-medium hover:text-blue-600 transition-colors"
                                title="编辑"
                              >
                                <span className="material-icons text-sm">edit</span>
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeletePrompt(prompt);
                                }}
                                className="p-1 text-text-medium hover:text-red-600 transition-colors"
                                title="删除"
                              >
                                <span className="material-icons text-sm">delete</span>
                              </button>
                            </div>
                          )}
                        </div>

                        <p className="text-text-medium text-sm mb-6 line-clamp-3">
                          {prompt.description || '无描述'}
                        </p>

                        <div className="mt-auto border-t border-[rgba(120,180,140,0.2)] w-full pt-3 px-4 flex justify-between items-center">
                          <div className="flex items-center text-xs text-text-light">
                            <span className="material-icons text-text-light text-sm mr-1">schedule</span>
                            <span>{new Date(prompt.updated_at).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center text-xs text-text-light">
                            <span className="material-icons text-text-light text-sm mr-1">person</span>
                            <span>{prompt.author_display_id || '未知用户'}</span>
                          </div>
                        </div>
                      </div>
                      <div className="page-curl"></div>
                    </div>
                  );
                })
              ) : (
                // 无提示词提示
                <div className="w-full ghibli-card p-12 flex flex-col items-center justify-center" style={{ minHeight: '320px' }}>
                  <div className="w-24 h-24 bg-[rgba(120,180,140,0.1)] rounded-full flex items-center justify-center mb-4 text-text-light">
                    <span className="material-icons text-4xl">search_off</span>
                  </div>
                  <h3 className="text-xl font-semibold text-text-dark mb-2 font-ma-shan">暂无提示词</h3>
                  <p className="text-text-medium text-center max-w-md mb-6">
                    {searchTerm
                      ? `没有找到包含"${searchTerm}"的提示词`
                      : `暂无${promptTypeMap[activeType as keyof typeof promptTypeMap]?.label}类型的用户提示词。`}
                  </p>
                  {searchTerm && (
                    <button
                      className="ghibli-button"
                      onClick={() => setSearchTerm('')}
                    >
                      <span className="material-icons text-sm mr-2">clear</span>
                      清除搜索
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </main>


      </div>

      {/* 提示词表单模态窗口 */}
      <PromptFormModal
        isOpen={showFormModal}
        onClose={() => {
          setShowFormModal(false);
          setEditingPrompt(null);
        }}
        onSave={handleSavePrompt}
        prompt={editingPrompt}
        promptType={activeType}
      />

      {/* 删除确认模态窗口 */}
      <PromptDeleteModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingPrompt(null);
        }}
        onConfirm={handleConfirmDelete}
        prompt={deletingPrompt}
      />
    </div>
  );
}
