'use client';

import React, { useState, useEffect } from 'react';
import { StarLogo } from '@/components/common/StarLogo';
import { Character } from '@/types/character';
import { Chapter } from '@/types/chapter';
import { Outline } from '@/types/outline';
import { Setting } from '@/types/setting';
import { Knowledge } from '@/types/knowledge';

// 侧边栏收起状态的localStorage键
const SIDEBAR_COLLAPSED_KEY = 'sidebar_collapsed';

interface SidebarProps {
  // 通用属性
  activeMenu?: string;
  // 章节管理相关属性
  chapters: Chapter[];
  activeChapter: number;
  onChapterClick: (index: number) => void;
  onAddChapter: () => void;
  onDeleteChapter: (index: number) => void;
  onEditChapterTitle: (index: number, newTitle: string) => void;
  onMoveChapter: (fromIndex: number, toIndex: number) => void;
  isDescending: boolean;
  setIsDescending: (value: boolean | ((prev: boolean) => boolean)) => void;
  // 角色卡管理相关属性
  characters: Character[];
  activeCharacter: string | null;
  onCharacterClick: (characterId: string) => void;
  onAddCharacter: () => void;
  onEditCharacterName: (characterId: string, newName: string) => void;
  onDeleteCharacter: (characterId: string) => void;
  // 大纲管理相关属性
  outlines: Outline[];
  activeOutline: string | null;
  onOutlineClick: (outlineId: string) => void;
  onAddOutline: () => void;
  onEditOutlineTitle: (outlineId: string, newTitle: string) => void;
  onDeleteOutline: (outlineId: string) => void;
  // 设定管理相关属性
  settings: Setting[];
  activeSetting: string | null;
  onSettingClick: (settingId: string) => void;
  onAddSetting: () => void;
  onEditSettingTitle: (settingId: string, newTitle: string) => void;
  onDeleteSetting: (settingId: string) => void;
  // 知识库管理相关属性
  knowledges: Knowledge[];
  activeKnowledge: number | null;
  onKnowledgeClick: (knowledgeId: number) => void;
  onAddKnowledge: () => void;
  onEditKnowledgeTitle: (knowledgeId: number, newTitle: string) => void;
  onDeleteKnowledge: (knowledgeId: number) => void;
}

export default function Sidebar({
  activeMenu,
  chapters,
  activeChapter,
  onChapterClick,
  onAddChapter,
  onDeleteChapter,
  onEditChapterTitle,
  onMoveChapter,
  isDescending,
  setIsDescending,
  characters,
  activeCharacter,
  onCharacterClick,
  onAddCharacter,
  onEditCharacterName,
  onDeleteCharacter,
  outlines,
  activeOutline,
  onOutlineClick,
  onAddOutline,
  onEditOutlineTitle,
  onDeleteOutline,
  settings,
  activeSetting,
  onSettingClick,
  onAddSetting,
  onEditSettingTitle,
  onDeleteSetting,
  knowledges,
  activeKnowledge,
  onKnowledgeClick,
  onAddKnowledge,
  onEditKnowledgeTitle,
  onDeleteKnowledge
}: SidebarProps) {
  // 章节列表展开/收起状态
  const [isChapterListExpanded, setIsChapterListExpanded] = useState(true);
  // 角色列表展开/收起状态
  const [isCharacterListExpanded, setIsCharacterListExpanded] = useState(true);
  // 大纲列表展开/收起状态
  const [isOutlineListExpanded, setIsOutlineListExpanded] = useState(true);
  // 设定列表展开/收起状态
  const [isSettingListExpanded, setIsSettingListExpanded] = useState(true);
  // 知识库列表展开/收起状态
  const [isKnowledgeListExpanded, setIsKnowledgeListExpanded] = useState(true);
  // 章节编辑状态
  const [editingChapterIndex, setEditingChapterIndex] = useState<number | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  // 大纲编辑状态
  const [editingOutlineId, setEditingOutlineId] = useState<string | null>(null);
  const [editingOutlineTitle, setEditingOutlineTitle] = useState('');
  // 设定编辑状态
  const [editingSettingId, setEditingSettingId] = useState<string | null>(null);
  const [editingSettingTitle, setEditingSettingTitle] = useState('');
  // 角色编辑状态
  const [editingCharacterId, setEditingCharacterId] = useState<string | null>(null);
  const [editingCharacterName, setEditingCharacterName] = useState('');
  // 知识库编辑状态
  const [editingKnowledgeId, setEditingKnowledgeId] = useState<number | null>(null);
  const [editingKnowledgeTitle, setEditingKnowledgeTitle] = useState('');
  // 拖拽状态
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  // 使用 useState 初始化为 false，然后在 useEffect 中从 localStorage 加载
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 在客户端加载时从 localStorage 获取状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem(SIDEBAR_COLLAPSED_KEY);
      if (savedState === 'true') {
        setIsCollapsed(true);
      }
    }
  }, []);

  // 监听侧边栏状态变化，动态设置body的CSS类
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const body = document.body;
      if (isCollapsed) {
        body.classList.add('sidebar-collapsed');
      } else {
        body.classList.remove('sidebar-collapsed');
      }
    }
  }, [isCollapsed]);

  // 章节编辑相关函数
  const startEditingChapter = (index: number, currentTitle: string) => {
    setEditingChapterIndex(index);
    setEditingTitle(currentTitle);
  };

  const saveChapterTitle = () => {
    if (editingChapterIndex !== null && onEditChapterTitle) {
      onEditChapterTitle(editingChapterIndex, editingTitle);
    }
    cancelEditing();
  };

  const cancelEditing = () => {
    setEditingChapterIndex(null);
    setEditingTitle('');
  };

  // 大纲编辑函数
  const startEditingOutline = (id: string, currentTitle: string) => {
    setEditingOutlineId(id);
    setEditingOutlineTitle(currentTitle);
  };

  const saveOutlineTitle = () => {
    if (editingOutlineId !== null && editingOutlineTitle.trim()) {
      onEditOutlineTitle(editingOutlineId, editingOutlineTitle);
    }
    cancelOutlineEditing();
  };

  const cancelOutlineEditing = () => {
    setEditingOutlineId(null);
    setEditingOutlineTitle('');
  };

  // 设定编辑函数
  const startEditingSetting = (id: string, currentTitle: string) => {
    setEditingSettingId(id);
    setEditingSettingTitle(currentTitle);
  };

  const saveSettingTitle = () => {
    if (editingSettingId !== null && editingSettingTitle.trim()) {
      onEditSettingTitle(editingSettingId, editingSettingTitle);
    }
    cancelSettingEditing();
  };

  const cancelSettingEditing = () => {
    setEditingSettingId(null);
    setEditingSettingTitle('');
  };

  // 角色编辑函数
  const startEditingCharacter = (id: string, currentName: string) => {
    setEditingCharacterId(id);
    setEditingCharacterName(currentName);
  };

  const saveCharacterName = () => {
    if (editingCharacterId !== null && editingCharacterName.trim()) {
      onEditCharacterName(editingCharacterId, editingCharacterName);
    }
    cancelCharacterEditing();
  };

  const cancelCharacterEditing = () => {
    setEditingCharacterId(null);
    setEditingCharacterName('');
  };

  // 知识库编辑函数
  const startEditingKnowledge = (id: number, currentTitle: string) => {
    setEditingKnowledgeId(id);
    setEditingKnowledgeTitle(currentTitle);
  };

  const saveKnowledgeTitle = () => {
    if (editingKnowledgeId !== null && editingKnowledgeTitle.trim()) {
      onEditKnowledgeTitle(editingKnowledgeId, editingKnowledgeTitle);
    }
    cancelKnowledgeEditing();
  };

  const cancelKnowledgeEditing = () => {
    setEditingKnowledgeId(null);
    setEditingKnowledgeTitle('');
  };

  // 拖拽相关函数
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== dropIndex && onMoveChapter) {
      onMoveChapter(draggedIndex, dropIndex);
    }
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  return (
    <>
      {/* 收起状态下只显示展开按钮 */}
      {isCollapsed ? (
        <button
          className="fixed left-0 top-1/2 transform -translate-y-1/2 bg-card-color p-1.5 rounded-r-lg shadow-md border border-l-0 border-accent-brown/30 text-text-medium hover:text-primary-green transition-colors duration-200 z-[100]"
          onClick={() => {
            setIsCollapsed(false);
            localStorage.setItem(SIDEBAR_COLLAPSED_KEY, 'false');
          }}
          aria-label="展开侧边栏"
          style={{ marginLeft: '0' }}
        >
          <span className="material-icons">chevron_right</span>
        </button>
      ) : (
        <div className="sidebar fixed top-0 left-0 h-screen w-64 border-r border-[rgba(120,180,140,0.2)] bg-card-color shadow-md flex flex-col rounded-tr-2xl rounded-br-2xl transition-all duration-300 z-50" style={{ position: 'fixed' }}>
          <div className="p-5 border-b border-[rgba(120,180,140,0.2)] flex items-center">
            <StarLogo size={48} className="mr-3" />
            <span
              className="font-bold"
              style={{
                fontSize: '1.25rem',
                fontWeight: '700',
                color: '#7D85CC'
              }}
            >
              星河AI写作
            </span>
          </div>

          <div className="flex-1 flex flex-col overflow-hidden">
            {/* 工具栏区域 - 上面50% */}
            <div style={{ height: '50%' }} className="border-b border-[rgba(120,180,140,0.2)] flex flex-col overflow-hidden">
              <div className="flex-1 overflow-y-auto">
                {/* 大纲管理 */}
                <div className="border-b border-[rgba(120,180,140,0.1)]">
                  <div className="px-3 py-2">
                    <div className="flex items-center justify-between">
                      <button
                        className="flex items-center text-base font-semibold text-text-medium hover:text-primary-green transition-colors duration-200"
                        onClick={() => setIsOutlineListExpanded(!isOutlineListExpanded)}
                      >
                        <span className="material-icons text-lg mr-1">
                          {isOutlineListExpanded ? 'expand_more' : 'chevron_right'}
                        </span>
                        大纲
                      </button>
                      <div className="flex items-center">
                        {onAddOutline && (
                          <button
                            className="w-5 h-5 flex items-center justify-center rounded-full bg-[#4a90e2] text-white hover:bg-[#357abd] transition-colors"
                            onClick={onAddOutline}
                            title="添加新大纲"
                          >
                            <span className="material-icons text-xs">add</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  {isOutlineListExpanded && (
                    <div className="px-3 pb-2">
                      {outlines.length === 0 ? (
                        <div className="py-2 text-center text-gray-500 text-xs">
                          暂无大纲
                        </div>
                      ) : (
                        outlines.map(outline => (
                          editingOutlineId === outline.id ? (
                            // 编辑模式 - 专用布局
                            <div
                              key={outline.id}
                              className="flex items-center py-2 px-3 bg-[rgba(90,157,107,0.1)] rounded-lg mx-1 my-0.5 border border-primary-green/30"
                            >
                              <input
                                type="text"
                                value={editingOutlineTitle}
                                onChange={(e) => setEditingOutlineTitle(e.target.value)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    saveOutlineTitle();
                                  } else if (e.key === 'Escape') {
                                    cancelOutlineEditing();
                                  }
                                }}
                                className="flex-1 text-sm bg-white border border-gray-300 rounded px-2 py-1 mr-2 focus:outline-none focus:border-primary-green"
                                autoFocus
                              />
                              <button
                                onClick={saveOutlineTitle}
                                className="w-6 h-6 flex items-center justify-center rounded bg-green-600 text-white hover:bg-green-700 mr-1"
                                title="确认"
                              >
                                <span className="material-icons text-sm">check</span>
                              </button>
                              <button
                                onClick={cancelOutlineEditing}
                                className="w-6 h-6 flex items-center justify-center rounded bg-gray-500 text-white hover:bg-gray-600"
                                title="取消"
                              >
                                <span className="material-icons text-sm">close</span>
                              </button>
                            </div>
                          ) : (
                            // 默认模式 - 正常布局
                            <div
                              key={outline.id}
                              className={`group relative flex items-center py-2 px-3 cursor-pointer transition-colors duration-200 rounded-lg mx-1 my-0.5 ${activeOutline === outline.id ? 'bg-[rgba(90,157,107,0.1)]' : 'hover:bg-[rgba(90,157,107,0.05)]'}`}
                              onClick={() => onOutlineClick && onOutlineClick(outline.id)}
                            >
                              <span className="material-icons text-sm text-[#4a90e2] mr-2">list_alt</span>
                              <div className="flex-1 min-w-0">
                                <span className={`text-sm truncate block ${activeOutline === outline.id ? 'text-primary-green font-semibold' : 'text-text-medium'}`}>
                                  {outline.title}
                                </span>
                              </div>
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditingOutline(outline.id, outline.title);
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-gray-500 hover:bg-gray-50"
                                  title="编辑大纲"
                                >
                                  <span className="material-icons text-xs">edit</span>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (window.confirm('确定要删除这个大纲吗？')) {
                                      onDeleteOutline(outline.id);
                                    }
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-red-500 hover:bg-red-50"
                                  title="删除大纲"
                                >
                                  <span className="material-icons text-xs">delete</span>
                                </button>
                              </div>
                            </div>
                          )
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* 设定管理 */}
                <div className="border-b border-[rgba(120,180,140,0.1)]">
                  <div className="px-3 py-2">
                    <div className="flex items-center justify-between">
                      <button
                        className="flex items-center text-base font-semibold text-text-medium hover:text-primary-green transition-colors duration-200"
                        onClick={() => setIsSettingListExpanded(!isSettingListExpanded)}
                      >
                        <span className="material-icons text-lg mr-1">
                          {isSettingListExpanded ? 'expand_more' : 'chevron_right'}
                        </span>
                        设定
                      </button>
                      <div className="flex items-center">
                        {onAddSetting && (
                          <button
                            className="w-5 h-5 flex items-center justify-center rounded-full bg-[#f39c12] text-white hover:bg-[#d68910] transition-colors"
                            onClick={onAddSetting}
                            title="添加新设定"
                          >
                            <span className="material-icons text-xs">add</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  {isSettingListExpanded && (
                    <div className="px-3 pb-2">
                      {settings.length === 0 ? (
                        <div className="py-2 text-center text-gray-500 text-xs">
                          暂无设定
                        </div>
                      ) : (
                        settings.map(setting => (
                          editingSettingId === setting.id ? (
                            // 编辑模式 - 专用布局
                            <div
                              key={setting.id}
                              className="flex items-center py-2 px-3 bg-[rgba(90,157,107,0.1)] rounded-lg mx-1 my-0.5 border border-primary-green/30"
                            >
                              <input
                                type="text"
                                value={editingSettingTitle}
                                onChange={(e) => setEditingSettingTitle(e.target.value)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    saveSettingTitle();
                                  } else if (e.key === 'Escape') {
                                    cancelSettingEditing();
                                  }
                                }}
                                className="flex-1 text-sm bg-white border border-gray-300 rounded px-2 py-1 mr-2 focus:outline-none focus:border-primary-green"
                                autoFocus
                              />
                              <button
                                onClick={saveSettingTitle}
                                className="w-6 h-6 flex items-center justify-center rounded bg-green-600 text-white hover:bg-green-700 mr-1"
                                title="确认"
                              >
                                <span className="material-icons text-sm">check</span>
                              </button>
                              <button
                                onClick={cancelSettingEditing}
                                className="w-6 h-6 flex items-center justify-center rounded bg-gray-500 text-white hover:bg-gray-600"
                                title="取消"
                              >
                                <span className="material-icons text-sm">close</span>
                              </button>
                            </div>
                          ) : (
                            // 默认模式 - 正常布局
                            <div
                              key={setting.id}
                              className={`group relative flex items-center py-2 px-3 cursor-pointer transition-colors duration-200 rounded-lg mx-1 my-0.5 ${activeSetting === setting.id ? 'bg-[rgba(90,157,107,0.1)]' : 'hover:bg-[rgba(90,157,107,0.05)]'}`}
                              onClick={() => onSettingClick && onSettingClick(setting.id)}
                            >
                              <span className="material-icons text-sm text-[#f39c12] mr-2">settings</span>
                              <div className="flex-1 min-w-0">
                                <span className={`text-sm truncate block ${activeSetting === setting.id ? 'text-primary-green font-semibold' : 'text-text-medium'}`}>
                                  {setting.title}
                                </span>
                              </div>
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditingSetting(setting.id, setting.title);
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-gray-500 hover:bg-gray-50"
                                  title="编辑设定"
                                >
                                  <span className="material-icons text-xs">edit</span>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (window.confirm('确定要删除这个设定吗？')) {
                                      onDeleteSetting(setting.id);
                                    }
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-red-500 hover:bg-red-50"
                                  title="删除设定"
                                >
                                  <span className="material-icons text-xs">delete</span>
                                </button>
                              </div>
                            </div>
                          )
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* 角色卡管理 */}
                <div className="border-b border-[rgba(120,180,140,0.1)]">
                  <div className="px-3 py-2">
                    <div className="flex items-center justify-between">
                      <button
                        className="flex items-center text-base font-semibold text-text-medium hover:text-primary-green transition-colors duration-200"
                        onClick={() => setIsCharacterListExpanded(!isCharacterListExpanded)}
                      >
                        <span className="material-icons text-lg mr-1">
                          {isCharacterListExpanded ? 'expand_more' : 'chevron_right'}
                        </span>
                        角色
                      </button>
                      <div className="flex items-center">
                        {onAddCharacter && (
                          <button
                            className="w-5 h-5 flex items-center justify-center rounded-full bg-[#9c6fe0] text-white hover:bg-[#8c5fd0] transition-colors"
                            onClick={onAddCharacter}
                            title="添加新角色"
                          >
                            <span className="material-icons text-xs">add</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  {isCharacterListExpanded && (
                    <div className="px-3 pb-2">
                      {characters.length === 0 ? (
                        <div className="py-2 text-center text-gray-500 text-xs">
                          暂无角色
                        </div>
                      ) : (
                        characters.map(character => (
                          editingCharacterId === character.id ? (
                            // 编辑模式 - 专用布局
                            <div
                              key={character.id}
                              className="flex items-center py-2 px-3 bg-[rgba(90,157,107,0.1)] rounded-lg mx-1 my-0.5 border border-primary-green/30"
                            >
                              <input
                                type="text"
                                value={editingCharacterName}
                                onChange={(e) => setEditingCharacterName(e.target.value)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    saveCharacterName();
                                  } else if (e.key === 'Escape') {
                                    cancelCharacterEditing();
                                  }
                                }}
                                className="flex-1 text-sm bg-white border border-gray-300 rounded px-2 py-1 mr-2 focus:outline-none focus:border-primary-green"
                                autoFocus
                              />
                              <button
                                onClick={saveCharacterName}
                                className="w-6 h-6 flex items-center justify-center rounded bg-green-600 text-white hover:bg-green-700 mr-1"
                                title="确认"
                              >
                                <span className="material-icons text-sm">check</span>
                              </button>
                              <button
                                onClick={cancelCharacterEditing}
                                className="w-6 h-6 flex items-center justify-center rounded bg-gray-500 text-white hover:bg-gray-600"
                                title="取消"
                              >
                                <span className="material-icons text-sm">close</span>
                              </button>
                            </div>
                          ) : (
                            // 默认模式 - 正常布局
                            <div
                              key={character.id}
                              className={`group relative flex items-center py-2 px-3 cursor-pointer transition-colors duration-200 rounded-lg mx-1 my-0.5 ${activeCharacter === character.id ? 'bg-[rgba(90,157,107,0.1)]' : 'hover:bg-[rgba(90,157,107,0.05)]'}`}
                              onClick={() => onCharacterClick && onCharacterClick(character.id)}
                            >
                              <span className="material-icons text-sm text-[#9c6fe0] mr-2">person</span>
                              <div className="flex-1 min-w-0">
                                <span className={`text-sm truncate block ${activeCharacter === character.id ? 'text-primary-green font-semibold' : 'text-text-medium'}`}>
                                  {character.name}
                                </span>
                              </div>
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditingCharacter(character.id, character.name);
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-gray-500 hover:bg-gray-50"
                                  title="编辑角色"
                                >
                                  <span className="material-icons text-xs">edit</span>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (window.confirm('确定要删除这个角色吗？')) {
                                      onDeleteCharacter(character.id);
                                    }
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-red-500 hover:bg-red-50"
                                  title="删除角色"
                                >
                                  <span className="material-icons text-xs">delete</span>
                                </button>
                              </div>
                            </div>
                          )
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* 知识库管理 */}
                <div>
                  <div className="px-3 py-2">
                    <div className="flex items-center justify-between">
                      <button
                        className="flex items-center text-base font-semibold text-text-medium hover:text-primary-green transition-colors duration-200"
                        onClick={() => setIsKnowledgeListExpanded(!isKnowledgeListExpanded)}
                      >
                        <span className="material-icons text-lg mr-1">
                          {isKnowledgeListExpanded ? 'expand_more' : 'chevron_right'}
                        </span>
                        知识库
                      </button>
                      <div className="flex items-center">
                        {onAddKnowledge && (
                          <button
                            className="w-5 h-5 flex items-center justify-center rounded-full bg-[#27ae60] text-white hover:bg-[#229954] transition-colors"
                            onClick={onAddKnowledge}
                            title="添加新知识"
                          >
                            <span className="material-icons text-xs">add</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  {isKnowledgeListExpanded && (
                    <div className="px-3 pb-2">
                      {knowledges.length === 0 ? (
                        <div className="py-2 text-center text-gray-500 text-xs">
                          暂无知识
                        </div>
                      ) : (
                        knowledges.map(knowledge => (
                          editingKnowledgeId === knowledge.id ? (
                            // 编辑模式 - 专用布局
                            <div
                              key={knowledge.id}
                              className="flex items-center py-2 px-3 bg-[rgba(90,157,107,0.1)] rounded-lg mx-1 my-0.5 border border-primary-green/30"
                            >
                              <input
                                type="text"
                                value={editingKnowledgeTitle}
                                onChange={(e) => setEditingKnowledgeTitle(e.target.value)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    saveKnowledgeTitle();
                                  } else if (e.key === 'Escape') {
                                    cancelKnowledgeEditing();
                                  }
                                }}
                                className="flex-1 text-sm bg-white border border-gray-300 rounded px-2 py-1 mr-2 focus:outline-none focus:border-primary-green"
                                autoFocus
                              />
                              <button
                                onClick={saveKnowledgeTitle}
                                className="w-6 h-6 flex items-center justify-center rounded bg-green-600 text-white hover:bg-green-700 mr-1"
                                title="确认"
                              >
                                <span className="material-icons text-sm">check</span>
                              </button>
                              <button
                                onClick={cancelKnowledgeEditing}
                                className="w-6 h-6 flex items-center justify-center rounded bg-gray-500 text-white hover:bg-gray-600"
                                title="取消"
                              >
                                <span className="material-icons text-sm">close</span>
                              </button>
                            </div>
                          ) : (
                            // 默认模式 - 正常布局
                            <div
                              key={knowledge.id}
                              className={`group relative flex items-center py-2 px-3 cursor-pointer transition-colors duration-200 rounded-lg mx-1 my-0.5 ${activeKnowledge === knowledge.id ? 'bg-[rgba(90,157,107,0.1)]' : 'hover:bg-[rgba(90,157,107,0.05)]'}`}
                              onClick={() => onKnowledgeClick && onKnowledgeClick(knowledge.id!)}
                            >
                              <span className="material-icons text-sm text-[#27ae60] mr-2">library_books</span>
                              <div className="flex-1 min-w-0">
                                <span className={`text-sm truncate block ${activeKnowledge === knowledge.id ? 'text-primary-green font-semibold' : 'text-text-medium'}`}>
                                  {knowledge.title}
                                </span>
                              </div>
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditingKnowledge(knowledge.id!, knowledge.title);
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-gray-500 hover:bg-gray-50"
                                  title="编辑知识"
                                >
                                  <span className="material-icons text-xs">edit</span>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (window.confirm('确定要删除这个知识吗？')) {
                                      onDeleteKnowledge(knowledge.id!);
                                    }
                                  }}
                                  className="w-5 h-5 flex items-center justify-center rounded text-red-500 hover:bg-red-50"
                                  title="删除知识"
                                >
                                  <span className="material-icons text-xs">delete</span>
                                </button>
                              </div>
                            </div>
                          )
                        ))
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 章节管理栏区域 - 下面50% */}
            <div style={{ height: '50%' }} className="flex flex-col">
              {/* 章节管理标题 */}
              <div className="px-3 py-2 border-b border-[rgba(120,180,140,0.1)]">
                <div className="flex items-center justify-between">
                  <button
                    className="flex items-center text-base font-semibold text-text-medium hover:text-primary-green transition-colors duration-200"
                    onClick={() => setIsChapterListExpanded(!isChapterListExpanded)}
                  >
                    <span className="material-icons text-lg mr-1">
                      {isChapterListExpanded ? 'expand_more' : 'chevron_right'}
                    </span>
                    章节管理
                  </button>
                  <div className="flex items-center">
                    {setIsDescending && (
                      <button
                        onClick={() => setIsDescending(prev => !prev)}
                        className="w-6 h-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 hover:bg-gray-300 transition-colors mr-2"
                        title={isDescending ? "切换为升序" : "切换为降序"}
                      >
                        <span className="material-icons text-sm">
                          {isDescending ? 'arrow_upward' : 'arrow_downward'}
                        </span>
                      </button>
                    )}
                    {onAddChapter && (
                      <button
                        className="w-6 h-6 flex items-center justify-center rounded-full bg-primary-green text-white hover:bg-[#4a8d5b] transition-colors"
                        onClick={onAddChapter}
                        title="添加新章节"
                      >
                        <span className="material-icons text-sm">add</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* 章节列表 - 可滚动区域 */}
              <div className="flex-1 overflow-y-auto px-3 py-2">
                {isChapterListExpanded && (
                  <>
                    {chapters.length === 0 ? (
                      <div className="py-4 text-center text-gray-500 text-sm">
                        暂无章节
                      </div>
                    ) : (
                      [...chapters]
                        .map((chapter, index) => ({ chapter, index }))
                        .sort((a, b) => isDescending ? b.chapter.order - a.chapter.order : a.chapter.order - b.chapter.order)
                        .map(({ chapter, index }) => {
                          const isEditing = editingChapterIndex === index;
                          const displayTitle = chapter.title ? chapter.title : `第 ${index + 1} 章`;

                          return (
                            <div
                              key={index}
                              onDragOver={(e) => handleDragOver(e, index)}
                              onDragLeave={handleDragLeave}
                              onDrop={(e) => handleDrop(e, index)}
                              className={`group relative ${activeChapter === index ? 'active bg-[rgba(90,157,107,0.1)]' : ''} ${isEditing ? 'bg-[rgba(90,157,107,0.1)]' : ''} ${draggedIndex === index ? 'opacity-50' : ''} ${dragOverIndex === index ? 'border-t-2 border-blue-500' : ''} hover:bg-[rgba(90,157,107,0.05)] transition-colors duration-200 rounded-lg mx-1 my-0.5`}
                            >
                              {/* 章节内容区域 */}
                              <div
                                className={`flex items-center py-2 px-3 cursor-pointer ${!isEditing ? 'menu-item-content' : ''}`}
                                onClick={() => !isEditing && onChapterClick && onChapterClick(index)}
                              >
                                {/* 拖拽手柄 */}
                                {!isEditing && (
                                  <div
                                    draggable
                                    onDragStart={(e) => handleDragStart(e, index)}
                                    onDragEnd={handleDragEnd}
                                    className="w-4 h-4 flex items-center justify-center cursor-move mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                    title="拖拽排序"
                                  >
                                    <span className="material-icons text-xs text-gray-400">drag_indicator</span>
                                  </div>
                                )}

                                {/* 章节标题 */}
                                <div className="flex-1 min-w-0">
                                  {isEditing ? (
                                    <input
                                      type="text"
                                      value={editingTitle}
                                      onChange={(e) => setEditingTitle(e.target.value)}
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                          saveChapterTitle();
                                        } else if (e.key === 'Escape') {
                                          cancelEditing();
                                        }
                                      }}
                                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-primary-green"
                                      autoFocus
                                    />
                                  ) : (
                                    <span className={`text-sm truncate block ${activeChapter === index ? 'text-primary-green font-semibold' : 'text-text-medium'}`}>
                                      {displayTitle}
                                    </span>
                                  )}
                                </div>
                              </div>

                              {/* 右侧操作按钮 */}
                              {isEditing ? (
                                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      saveChapterTitle();
                                    }}
                                    className="w-5 h-5 flex items-center justify-center rounded text-green-600 hover:bg-green-50"
                                    title="保存"
                                  >
                                    <span className="material-icons text-xs">check</span>
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      cancelEditing();
                                    }}
                                    className="w-5 h-5 flex items-center justify-center rounded text-gray-500 hover:bg-gray-50"
                                    title="取消"
                                  >
                                    <span className="material-icons text-xs">close</span>
                                  </button>
                                </div>
                              ) : (
                                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                  {onEditChapterTitle && (
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        startEditingChapter(index, displayTitle);
                                      }}
                                      className="w-5 h-5 flex items-center justify-center rounded text-gray-500 hover:bg-gray-50"
                                      title="编辑标题"
                                    >
                                      <span className="material-icons text-xs">edit</span>
                                    </button>
                                  )}
                                  {onDeleteChapter && (
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        if (window.confirm('确定要删除这个章节吗？')) {
                                          onDeleteChapter(index);
                                        }
                                      }}
                                      className="w-5 h-5 flex items-center justify-center rounded text-red-500 hover:bg-red-50"
                                      title="删除章节"
                                    >
                                      <span className="material-icons text-xs">delete</span>
                                    </button>
                                  )}
                                </div>
                              )}
                            </div>
                          );
                        })
                    )}
                  </>
                )}
              </div>
            </div>
          </div>

          {/* 收起/展开按钮 */}
          <div className="mt-auto p-3 border-t border-[rgba(120,180,140,0.2)] flex justify-center">
            <button
              className="w-full flex items-center justify-center py-2 rounded-xl text-text-medium hover:bg-[rgba(120,180,140,0.1)] transition-colors duration-200"
              onClick={() => {
                setIsCollapsed(true);
                localStorage.setItem(SIDEBAR_COLLAPSED_KEY, 'true');
              }}
              aria-label="收起侧边栏"
            >
              <span className="material-icons">chevron_left</span>
              <span className="ml-2">收起</span>
            </button>
          </div>
        </div>
      )}
    </>
  );
}
